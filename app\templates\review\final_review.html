<!-- 最终审核页面 -->
<style>
/* 图片预览模态框样式 - 确保显示在最前面 */
#imageModal {
    z-index: 1070 !important;
}

#imageModal.show {
    z-index: 1070 !important;
}

/* 确保backdrop正确清理 */
.modal-backdrop {
    transition: opacity 0.15s linear;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* 防止backdrop残留 */
body.modal-open .modal-backdrop:not(.show) {
    display: none !important;
}

.content-preview-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
    margin: 5px 0;
}

/* 自定义驳回模态框样式 */
#customRejectModal .modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

#customRejectModal .quick-reason-btn {
    transition: all 0.2s ease;
    border-radius: 20px;
    font-size: 12px;
    padding: 4px 12px;
}

#customRejectModal .quick-reason-btn:hover {
    background-color: #6c757d;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#customRejectModal .form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220,53,69,.25);
}

#customRejectModal .modal-footer .btn {
    min-width: 100px;
}

.content-info {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.content-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 8px;
}

.content-text {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #007bff;
    margin-bottom: 10px;
}

.content-text p {
    margin-bottom: 0;
    line-height: 1.6;
    color: #495057;
}

.content-meta {
    margin-top: 8px;
}

.content-images {
    margin-top: 15px;
}

.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.image-item:hover {
    transform: scale(1.05);
}

.image-order {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.no-images {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.table td {
    vertical-align: top;
    padding: 15px 8px;
}

.btn-group-vertical .btn {
    margin-bottom: 2px;
}

/* 左侧信息面板样式 */
.info-panel {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.section {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
}

.section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.location-info {
    color: #6c757d;
    font-size: 14px;
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 正方形图片网格样式 */
.images-preview {
    text-align: center;
}

.image-grid-square {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 8px;
    max-width: 200px;
    margin: 0 auto;
}

.image-item-square {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 2px solid #e9ecef;
}

.image-item-square:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.square-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.image-order-square {
    position: absolute;
    top: 2px;
    left: 2px;
    background: rgba(0,0,0,0.8);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

/* 文案内容样式优化 */
.content-preview-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: white;
}

.content-title {
    color: #495057;
    font-weight: 600;
    font-size: 16px;
}

.content-text {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 3px solid #007bff;
    margin-top: 10px;
}

.content-text p {
    margin-bottom: 0;
    line-height: 1.6;
    color: #495057;
}

/* 表格行样式 */
.table td {
    vertical-align: top;
    padding: 15px 8px;
}

/* 无图片状态 */
.no-images {
    padding: 20px;
    color: #6c757d;
}
</style>

<script>
// 全局变量
let currentContentId = null;

// 调试函数
function debugCurrentState() {
    console.log('=== 当前状态调试 ===');
    console.log('currentContentId:', currentContentId);
    console.log('页面中的所有 data-content-id 元素:');
    const elements = document.querySelectorAll('[data-content-id]');
    elements.forEach((el, index) => {
        console.log(`  ${index + 1}. ID: ${el.getAttribute('data-content-id')}, 元素:`, el);
    });
    console.log('驳回按钮:');
    const rejectButtons = document.querySelectorAll('button[onclick*="rejectContent"]');
    rejectButtons.forEach((btn, index) => {
        console.log(`  ${index + 1}. onclick: ${btn.getAttribute('onclick')}, 按钮:`, btn);
    });
}

// 将调试函数设为全局
window.debugCurrentState = debugCurrentState;

// 测试函数：手动设置contentId并触发驳回
function testReject(contentId) {
    console.log('=== 测试驳回功能 ===');
    console.log('手动设置 contentId:', contentId);
    currentContentId = parseInt(contentId);
    console.log('设置后的 currentContentId:', currentContentId);

    // 显示驳回模态框
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}
window.testReject = testReject;

// 简化的快捷理由填充函数
function fillQuickReason(reason) {
    console.log('填充快捷理由:', reason);
    const textarea = document.getElementById('rejectReason');
    if (textarea) {
        textarea.value = reason;
        console.log('快捷理由已填入');
    } else {
        console.error('找不到 rejectReason 输入框');
    }
}
window.fillQuickReason = fillQuickReason;

// 统一的模态框关闭函数，确保正确清理遮罩层
function closeModal(modalId) {
    console.log('关闭模态框:', modalId);

    try {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modalInstance = bootstrap.Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.hide();
            }
        }

        // 立即清理一次遮罩层
        cleanupModalBackdrop();

        // 延迟清理，确保动画完成后彻底清理
        setTimeout(() => {
            cleanupModalBackdrop();
            console.log('模态框遮罩层已清理');
        }, 300); // 等待动画完成

    } catch (error) {
        console.error('关闭模态框时出错:', error);
        // 如果出错，强制清理
        cleanupModalBackdrop();
    }
}

// 统一的遮罩层清理函数
function cleanupModalBackdrop() {
    // 移除所有模态框遮罩层
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
        if (backdrop && backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop);
        }
    });

    // 检查是否还有打开的模态框
    const openModals = document.querySelectorAll('.modal.show');
    if (openModals.length === 0) {
        // 没有打开的模态框，恢复body的样式
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        document.body.style.marginRight = '';
    }
}
window.closeModal = closeModal;
window.cleanupModalBackdrop = cleanupModalBackdrop;

// 简化的客户选择变更处理函数 - 放在最前面确保全局可用
function handleClientChange() {
    console.log('=== 客户选择变更 (简化版) ===');
    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');

    if (!clientSelect || !taskSelect) {
        console.error('找不到选择框元素');
        return;
    }

    const clientId = clientSelect.value;
    console.log('选择的客户ID:', clientId);

    if (clientId && clientId !== '') {
        // 显示加载状态
        taskSelect.innerHTML = '<option value="">加载中...</option>';
        taskSelect.disabled = true;

        // 调用API获取任务列表
        fetch(`/simple/api/get-final-review-client-tasks/${clientId}`)
            .then(response => {
                console.log('API响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API响应数据:', data);

                // 更新任务选择框
                taskSelect.innerHTML = '<option value="">全部任务</option>';
                taskSelect.disabled = false;

                if (data.success && data.tasks && data.tasks.length > 0) {
                    data.tasks.forEach(task => {
                        const option = document.createElement('option');
                        option.value = task.id;
                        option.textContent = task.name;
                        taskSelect.appendChild(option);
                    });
                    console.log(`✅ 成功加载 ${data.tasks.length} 个任务`);
                } else {
                    console.log('该客户没有最终审核任务');
                }
            })
            .catch(error => {
                console.error('API调用失败:', error);
                taskSelect.innerHTML = '<option value="">加载失败</option>';
                taskSelect.disabled = false;
            });
    } else {
        // 选择了"全部客户"，重置任务选择框
        console.log('选择了全部客户，重置任务选择框');
        taskSelect.innerHTML = '<option value="">全部任务</option>';
        taskSelect.disabled = false;
    }
}

// 任务选择变更处理函数
function handleTaskChange() {
    console.log('=== 任务选择变更 ===');
    const taskSelect = document.getElementById('task_filter');
    if (taskSelect) {
        console.log('选择的任务ID:', taskSelect.value);
    }
}

// 全选/取消全选功能
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('.content-checkbox');
    checkboxes.forEach(cb => cb.checked = checkbox.checked);

    // 同步两个全选复选框的状态
    const selectAllHeader = document.getElementById('selectAllHeader');
    if (selectAllHeader && selectAllHeader !== checkbox) {
        selectAllHeader.checked = checkbox.checked;
    }

    updateSelectedCount();
}

// 更新选中数量和批量操作按钮状态
function updateSelectedCount() {
    const selected = document.querySelectorAll('.content-checkbox:checked');
    const count = selected.length;

    // 更新批量驳回按钮状态
    const batchRejectBtn = document.getElementById('batchRejectBtn');
    if (batchRejectBtn) {
        batchRejectBtn.disabled = count === 0;
        const icon = '<i class="bi bi-x-circle"></i>';
        batchRejectBtn.innerHTML = count > 0 ? `${icon} 批量驳回 (${count})` : `${icon} 批量驳回`;
    }

    // 更新批量通过按钮状态
    const batchApproveBtn = document.getElementById('batchApproveBtn');
    if (batchApproveBtn) {
        batchApproveBtn.disabled = count === 0;
        const icon = '<i class="bi bi-check-circle"></i>';
        batchApproveBtn.innerHTML = count > 0 ? `${icon} 批量通过 (${count})` : `${icon} 批量通过`;
    }

    // 更新全选复选框状态
    const allCheckboxes = document.querySelectorAll('.content-checkbox');
    const selectAllHeader = document.getElementById('selectAllHeader');

    if (selectAllHeader && allCheckboxes.length > 0) {
        if (count === 0) {
            selectAllHeader.checked = false;
            selectAllHeader.indeterminate = false;
        } else if (count === allCheckboxes.length) {
            selectAllHeader.checked = true;
            selectAllHeader.indeterminate = false;
        } else {
            selectAllHeader.checked = false;
            selectAllHeader.indeterminate = true;
        }
    }
}

// 立即将函数添加到全局作用域 - 解决AJAX加载时的作用域问题
window.handleClientChange = handleClientChange;
window.handleTaskChange = handleTaskChange;
window.toggleSelectAll = toggleSelectAll;
window.updateSelectedCount = updateSelectedCount;
window.showBatchApproveModal = showBatchApproveModal;
window.showBatchRejectModal = showBatchRejectModal;
window.submitBatchApprove = submitBatchApprove;
window.submitBatchReject = submitBatchReject;

console.log('=== 函数已添加到全局作用域 ===');
console.log('window.handleClientChange:', typeof window.handleClientChange);
console.log('window.handleTaskChange:', typeof window.handleTaskChange);

// 查看文案详情
function viewContent(contentId) {
    console.log('查看文案详情，ID:', contentId);
    fetch(`/simple/api/contents/${contentId}/view`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        showContentModal('查看文案详情', html);
    })
    .catch(error => {
        console.error('加载文案详情失败:', error);
        alert('加载文案详情失败，请重试');
    });
}

// 确保函数在全局作用域中可访问
window.viewContent = viewContent;

// 审核通过
function approveContent(contentId) {
    // 直接执行通过，不再二次确认
    const formData = new FormData();
    formData.append('action', 'approve');
    formData.append('review_comment', '通过审核');

    fetch(`/simple/api/final-review/${contentId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('审核通过成功！', 'success');
            // 从列表中移除该行
            removeContentFromList(contentId);
        } else {
            showToast('操作失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('审核失败:', error);
        showToast('审核失败，请重试', 'error');
    });
}

// 确保函数在全局作用域中可访问
window.approveContent = approveContent;

// 驳回文案
function rejectContent(contentId, event) {
    console.log('=== rejectContent 被调用 ===');
    console.log('传入的 contentId:', contentId);
    console.log('当前全局 currentContentId:', currentContentId);

    // 确保 contentId 是数字
    const numericContentId = parseInt(contentId);
    console.log('转换后的 numericContentId:', numericContentId);

    if (isNaN(numericContentId) || numericContentId <= 0) {
        console.error('无效的 contentId:', contentId);
        showToast('错误：无法获取文案ID，请刷新页面重试', 'error');
        return;
    }

    // 确保全局变量也被设置（双重保险）
    currentContentId = numericContentId;
    window.currentContentId = numericContentId;
    console.log('最终设置的 currentContentId:', currentContentId);

    // 清空之前的输入
    const reasonTextarea = document.getElementById('rejectReason');
    if (reasonTextarea) {
        reasonTextarea.value = '';
    }

    // 重置驳回类型选择为默认值
    const contentTypeRadio = document.getElementById('type_content');
    if (contentTypeRadio) {
        contentTypeRadio.checked = true;
    }

    // 不再需要复杂的初始化逻辑，快捷理由按钮已经直接在HTML中定义

    // 设置隐藏字段的值
    document.getElementById('rejectContentId').value = currentContentId;

    // 显示新的驳回类型选择模态框
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();

    // 注意：快捷理由按钮的事件处理已经通过全局事件委托处理，不需要重新设置
}

// 确保函数在全局作用域中可访问
window.rejectContent = rejectContent;

// 提交自定义驳回
function submitCustomReject() {
    console.log('submitCustomReject函数被调用');
    const reason = document.getElementById('customRejectReason').value.trim();
    if (!reason) {
        showToast('请输入驳回理由', 'error');
        return;
    }

    console.log('驳回理由:', reason);
    console.log('当前内容ID:', currentContentId);

    // 执行驳回
    const formData = new FormData();
    formData.append('action', 'reject');
    formData.append('review_comment', reason);

    fetch(`/simple/api/final-review/${currentContentId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('驳回成功！', 'success');
            // 使用统一的关闭函数
            closeModal('customRejectModal');
            // 清空输入框
            document.getElementById('customRejectReason').value = '';
            // 从列表中移除该行
            removeContentFromList(currentContentId);
        } else {
            showToast('操作失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('驳回失败:', error);
        showToast('驳回失败，请重试', 'error');
    });
}

// 确保函数在全局作用域中可访问
window.rejectContent = rejectContent;

// 显示图片模态框
function showImageModal(imageSrc, imageName) {
    const modal = document.getElementById('imageModal');
    const modalTitle = document.getElementById('imageModalTitle');
    const modalImg = document.getElementById('imageModalImg');

    modalTitle.textContent = imageName || '查看图片';
    modalImg.src = imageSrc;

    // 确保模态框显示在最前面
    modal.style.zIndex = '1070';

    const bsModal = new bootstrap.Modal(modal, {
        backdrop: true,
        keyboard: true,
        focus: true
    });

    // 监听模态框显示事件，确保z-index正确
    modal.addEventListener('shown.bs.modal', function() {
        modal.style.zIndex = '1070';
        // 确保backdrop也有正确的z-index
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.style.zIndex = '1069';
        }
    });

    // 监听模态框隐藏事件，清理backdrop
    modal.addEventListener('hidden.bs.modal', function() {
        // 清理所有可能残留的backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop && backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });

        // 确保body没有modal-open类（如果没有其他模态框打开）
        const openModals = document.querySelectorAll('.modal.show');
        if (openModals.length === 0) {
            document.body.classList.remove('modal-open');
            document.body.style.paddingRight = '';
        }
    });

    bsModal.show();
}

// 全局审核函数 - 确保在任何情况下都能调用
function approveContentInModal(contentId) {
    console.log('全局通过审核函数被调用，contentId:', contentId);
    // 直接执行通过，不再二次确认
    const formData = new FormData();
    formData.append('action', 'approve');
    formData.append('review_comment', '通过审核');

    fetch(`/simple/api/final-review/${contentId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('审核通过成功！', 'success');
            // 关闭所有打开的模态框
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modalEl => {
                const modalId = modalEl.id;
                if (modalId) {
                    closeModal(modalId);
                }
            });
            // 从列表中移除该行
            removeContentFromList(contentId);
        } else {
            showToast('操作失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('审核失败:', error);
        showToast('审核失败，请重试', 'error');
    });
}

function rejectContentInModal(contentId) {
    console.log('全局驳回函数被调用，contentId:', contentId);
    const reason = document.getElementById('modalRejectReason').value.trim();
    if (!reason) {
        showToast('请输入驳回理由', 'error');
        return;
    }

    // 直接执行驳回，不再二次确认
    const formData = new FormData();
    formData.append('action', 'reject');
    formData.append('review_comment', reason);

    fetch(`/simple/api/final-review/${contentId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('驳回成功！', 'success');
            // 关闭所有打开的模态框
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modalEl => {
                const modalId = modalEl.id;
                if (modalId) {
                    closeModal(modalId);
                }
            });
            // 从列表中移除该行
            removeContentFromList(contentId);
        } else {
            showToast('操作失败：' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('驳回失败:', error);
        showToast('驳回失败，请重试', 'error');
    });
}

// 填入快捷理由（用于驳回类型选择弹窗）
function fillQuickReason(reason) {
    console.log('填入快捷理由:', reason);
    const textarea = document.getElementById('customRejectReason');

    if (!textarea) {
        console.error('找不到customRejectReason输入框');
        return;
    }

    // 如果输入框为空，直接填入；如果有内容，追加
    if (textarea.value.trim() === '') {
        textarea.value = reason;
    } else {
        textarea.value += '\n' + reason;
    }

    // 聚焦到输入框
    textarea.focus();

    console.log('快捷理由已填入，当前内容:', textarea.value);
}

// 填入驳回理由（用于主驳回弹窗）- 兼容旧的调用方式
function fillRejectReason(reason) {
    console.log('填入驳回理由:', reason);
    const textarea = document.getElementById('rejectReason');

    if (!textarea) {
        console.error('找不到rejectReason输入框');
        return;
    }

    // 直接设置文本框内容（不追加，因为标签系统会管理内容）
    textarea.value = reason;

    // 聚焦到文本框
    textarea.focus();

    console.log('驳回理由已填入，当前内容:', textarea.value);
}

// 处理快捷理由按钮点击
function handleQuickReasonClick(button) {
    const reason = button.textContent.trim();
    fillQuickReason(reason);
}

// 填入模态框快捷理由（用于查看详情弹窗）
function fillModalQuickReason(reason) {
    console.log('填入模态框快捷理由:', reason);
    const textarea = document.getElementById('modalRejectReason');

    if (!textarea) {
        console.error('找不到modalRejectReason输入框');
        return;
    }

    // 如果输入框为空，直接填入；如果有内容，追加
    if (textarea.value.trim() === '') {
        textarea.value = reason;
    } else {
        textarea.value += '\n' + reason;
    }

    // 聚焦到输入框
    textarea.focus();

    console.log('模态框快捷理由已填入，当前内容:', textarea.value);
}

// 确保函数在全局作用域中可访问
window.showImageModal = showImageModal;
window.showContentModal = showContentModal;
window.approveContentInModal = approveContentInModal;
window.rejectContentInModal = rejectContentInModal;
window.submitCustomReject = submitCustomReject;
window.fillQuickReason = fillQuickReason;
window.handleQuickReasonClick = handleQuickReasonClick;
window.fillModalQuickReason = fillModalQuickReason;

// 页面加载完成后初始化事件
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== 页面加载完成，开始初始化功能 ===');

    // 清理可能残留的模态框遮罩层
    cleanupModalBackdrop();

    // 添加全局模态框隐藏事件监听器，确保所有模态框关闭时都能正确清理遮罩层
    document.addEventListener('hidden.bs.modal', function(event) {
        console.log('模态框隐藏事件触发:', event.target.id);
        // 延迟清理，确保Bootstrap完成所有清理工作
        setTimeout(() => {
            cleanupModalBackdrop();
        }, 100);
    });

    // 检查页面中的关键元素
    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');
    const filterForm = document.getElementById('filterForm');

    console.log('DOM元素检查:');
    console.log('- client_filter:', clientSelect ? '✅ 找到' : '❌ 未找到');
    console.log('- task_filter:', taskSelect ? '✅ 找到' : '❌ 未找到');
    console.log('- filterForm:', filterForm ? '✅ 找到' : '❌ 未找到');

    // 初始化客户任务联动功能
    initClientTaskLinkage();

    // 不再需要复杂的事件委托，快捷理由按钮使用简单的onclick

    // 处理筛选表单提交
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('表单提交事件被触发');
            performFinalReviewSearch();
        });
    } else {
        console.log('❌ 筛选表单未找到，无法绑定提交事件');
    }

    console.log('=== 初始化完成 ===');

    // 添加事件委托处理驳回按钮点击和快捷理由按钮点击
    document.addEventListener('click', function(e) {
        // 处理驳回按钮点击
        const rejectButton = e.target.closest('button[onclick*="rejectContent"]');
        if (rejectButton) {
            console.log('通过事件委托捕获到驳回按钮点击');

            // 阻止默认的onclick执行
            e.preventDefault();
            e.stopPropagation();

            // 从按钮所在的行获取 content ID
            const row = rejectButton.closest('tr[data-content-id]');
            if (row) {
                const contentId = row.getAttribute('data-content-id');
                console.log('通过事件委托获取到 contentId:', contentId);

                // 直接调用驳回函数，传递事件对象
                rejectContent(contentId, e);
            } else {
                console.error('无法从行元素获取 content ID');
                showToast('错误：无法获取文案ID', 'error');
            }
            return;
        }

        // 处理快捷理由按钮点击
        const quickReasonButton = e.target.closest('.quick-reason-tag');
        if (quickReasonButton) {
            console.log('快捷理由按钮被点击');
            const reason = quickReasonButton.getAttribute('data-reason') || quickReasonButton.textContent.trim();
            appendQuickReason(reason);
            return;
        }
    });
});

// 追加快捷理由到文本框
function appendQuickReason(reason) {
    console.log('追加快捷理由:', reason);
    const textarea = document.getElementById('rejectReason');
    if (!textarea) {
        console.error('找不到驳回理由文本框');
        return;
    }

    const currentValue = textarea.value.trim();
    if (currentValue) {
        // 如果文本框已有内容，在新行追加
        textarea.value = currentValue + '\n' + reason;
    } else {
        // 如果文本框为空，直接设置
        textarea.value = reason;
    }

    // 聚焦到文本框并将光标移到末尾
    textarea.focus();
    textarea.setSelectionRange(textarea.value.length, textarea.value.length);

    console.log('快捷理由已追加，当前内容:', textarea.value);
}

// 将函数设为全局，方便调用
window.appendQuickReason = appendQuickReason;

// 测试函数 - 可以在控制台手动调用
window.testClientTaskAPI = function(clientId) {
    console.log(`=== 测试API: /simple/api/get-final-review-client-tasks/${clientId} ===`);

    fetch(`/simple/api/get-final-review-client-tasks/${clientId}`)
        .then(response => {
            console.log('API响应状态:', response.status);
            console.log('API响应头:', response.headers);
            return response.json();
        })
        .then(data => {
            console.log('API响应数据:', data);
        })
        .catch(error => {
            console.error('API调用失败:', error);
        });
};

// 测试函数 - 手动触发客户选择变更
window.testClientChange = function(clientId) {
    console.log(`=== 手动测试客户选择变更: ${clientId} ===`);
    const clientSelect = document.getElementById('client_filter');
    if (clientSelect) {
        clientSelect.value = clientId;
        clientSelect.onchange();
    } else {
        console.error('找不到客户选择框');
    }
};

// 重复的函数定义已移到页面顶部，这里删除

// 显示内容模态框
function showContentModal(title, content) {
    // 先关闭已存在的内容模态框
    const existingModal = document.getElementById('contentDetailModal');
    if (existingModal) {
        const existingInstance = bootstrap.Modal.getInstance(existingModal);
        if (existingInstance) {
            existingInstance.hide();
        }
        existingModal.remove();
    }

    // 简单的模态框实现
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'contentDetailModal'; // 添加固定ID
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">${content}</div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // 模态框关闭后清理DOM
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建Toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1080';
        document.body.appendChild(toastContainer);
    }

    // 创建Toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示Toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();

    // Toast隐藏后移除元素
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// 显示批量通过模态框
function showBatchApproveModal() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    const count = selectedCheckboxes.length;

    if (count === 0) {
        showToast('请先选择要通过审核的文案', 'warning');
        return;
    }

    // 更新模态框中的数量显示
    document.getElementById('batchApproveCount').textContent = count;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchApproveModal'));
    modal.show();
}

// 提交批量通过
function submitBatchApprove() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (contentIds.length === 0) {
        showToast('没有选中的文案', 'warning');
        return;
    }

    // 显示加载状态
    const submitBtn = document.querySelector('#batchApproveModal .btn-success');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>处理中...';

    // 批量通过请求 - 使用表单数据格式
    const promises = contentIds.map(contentId => {
        const formData = new FormData();
        formData.append('action', 'approve');
        formData.append('review_comment', '批量通过审核');

        return fetch(`/simple/api/final-review/${contentId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        }).then(response => response.json());
    });

    Promise.all(promises)
        .then(results => {
            console.log('批量通过结果:', results);

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            // 记录失败的详细信息
            results.forEach((result, index) => {
                if (!result.success) {
                    console.error(`文案 ${contentIds[index]} 审核失败:`, result.message);
                }
            });

            if (successCount > 0) {
                showToast(`成功通过 ${successCount} 篇文案${failCount > 0 ? `，${failCount} 篇失败` : ''}`,
                         failCount > 0 ? 'warning' : 'success');

                // 从列表中移除成功通过的内容
                contentIds.forEach((contentId, index) => {
                    if (results[index].success) {
                        removeContentFromList(contentId);
                    }
                });

                // 重置选择状态
                updateSelectedCount();
            } else {
                // 显示第一个失败的详细错误信息
                const firstError = results.find(r => !r.success);
                const errorMsg = firstError ? firstError.message : '批量通过失败，请重试';
                showToast(errorMsg, 'error');
            }

            // 关闭模态框
            closeModal('batchApproveModal');
        })
        .catch(error => {
            console.error('批量通过请求失败:', error);
            showToast('批量通过请求失败，请重试', 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
}

// 显示批量驳回模态框
function showBatchRejectModal() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    const count = selectedCheckboxes.length;

    if (count === 0) {
        showToast('请先选择要驳回的文案', 'warning');
        return;
    }

    // 更新模态框中的数量显示
    document.getElementById('batchRejectCount').textContent = count;

    // 清空之前的理由
    document.getElementById('batchRejectReason').value = '';

    // 重置驳回类型选择为默认值（文案问题）
    document.getElementById('batch_type_content').checked = true;

    // 重置卡片选中状态
    const batchTypeCards = document.querySelectorAll('#batchRejectModal .rejection-type-card');
    batchTypeCards.forEach(card => card.classList.remove('selected'));
    document.querySelector('#batchRejectModal .rejection-type-card[data-type="content"]').classList.add('selected');

    // 初始化批量驳回的驳回类型选择事件
    initBatchRejectionTypeSelection();

    // 更新快捷理由和流转说明
    updateBatchQuickReasonsAndFlow('content');

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchRejectModal'));
    modal.show();
}

// 填充批量驳回快捷理由 - 追加模式，确保全局可用
window.fillBatchQuickReason = function(reason) {
    console.log('fillBatchQuickReason 被调用，理由:', reason);
    const reasonInput = document.getElementById('batchRejectReason');
    if (reasonInput) {
        const currentValue = reasonInput.value.trim();
        if (currentValue) {
            // 如果已有内容，在后面添加新行和新理由
            reasonInput.value = currentValue + '\n' + reason;
        } else {
            // 如果没有内容，直接设置理由
            reasonInput.value = reason;
        }
        console.log('理由已追加到输入框，当前内容:', reasonInput.value);
    } else {
        console.error('找不到 batchRejectReason 输入框');
    }
}

// 初始化批量驳回的驳回类型选择
function initBatchRejectionTypeSelection() {
    // 为批量驳回的驳回类型卡片添加点击事件
    const batchTypeCards = document.querySelectorAll('#batchRejectModal .rejection-type-card');
    batchTypeCards.forEach(card => {
        card.addEventListener('click', function() {
            const type = this.dataset.type;
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
                // 更新卡片样式
                batchTypeCards.forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                // 更新快捷理由和流转说明
                updateBatchQuickReasonsAndFlow(type);
            }
        });
    });
}

// 更新批量驳回的快捷理由和流转说明
function updateBatchQuickReasonsAndFlow(type) {
    const quickReasonsContainer = document.getElementById('batchQuickReasons');
    const flowText = document.getElementById('batchFlowText');

    // 清空现有的快捷理由
    quickReasonsContainer.innerHTML = '';

    // 根据驳回类型生成快捷理由
    let reasons = [];
    let flowDescription = '';

    switch(type) {
        case 'content':
            reasons = [
                '标题不够吸引人，建议重新设计',
                '内容逻辑不清晰，需要重新组织',
                '话题标签不合适，需要调整',
                '文案长度不合适，需要调整',
                '内容与品牌调性不符，需要调整风格',
                '缺少关键信息，需要补充完整'
            ];
            flowDescription = '选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容';
            break;
        case 'image':
            reasons = [
                '图片质量不符合要求，需要更换',
                '图片内容与文案不匹配',
                '图片数量不足，需要补充',
                '图片尺寸或格式不合适',
                '图片清晰度不够，影响展示效果',
                '图片风格与品牌不符'
            ];
            flowDescription = '选择"图片问题"：文案将回到<strong>图片管理</strong>，需要重新上传或调整图片';
            break;
        case 'both':
            reasons = [
                '文案和图片都需要重新设计',
                '整体质量不符合标准，需要全面优化',
                '内容和视觉效果都需要提升',
                '文案与图片配合度不够，需要重新匹配',
                '整体创意需要重新构思',
                '品牌一致性问题，需要全面调整'
            ];
            flowDescription = '选择"两者都有问题"：文案将回到<strong>草稿状态</strong>，需要重新编写文案并上传图片';
            break;
    }

    // 生成快捷理由按钮
    reasons.forEach(reason => {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'btn btn-outline-secondary btn-sm quick-reason-btn';
        button.textContent = reason.split('，')[0]; // 只显示简短描述
        button.onclick = () => fillBatchQuickReason(reason);
        quickReasonsContainer.appendChild(button);
    });

    // 更新流转说明
    flowText.innerHTML = flowDescription;
}

// 提交批量驳回
function submitBatchReject() {
    const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
    const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    const reason = document.getElementById('batchRejectReason').value.trim();
    const rejectionType = document.querySelector('input[name="batch_rejection_type"]:checked').value;

    if (contentIds.length === 0) {
        showToast('没有选中的文案', 'warning');
        return;
    }

    if (!reason) {
        showToast('请填写驳回理由', 'warning');
        return;
    }

    // 显示加载状态
    const submitBtn = document.querySelector('#batchRejectModal .btn-danger');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>处理中...';

    // 批量驳回请求 - 使用表单数据格式
    const promises = contentIds.map(contentId => {
        const formData = new FormData();
        formData.append('action', 'reject');
        formData.append('review_comment', reason);
        formData.append('rejection_type', rejectionType);

        return fetch(`/simple/api/final-review/${contentId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        }).then(response => response.json());
    });

    Promise.all(promises)
        .then(results => {
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            if (successCount > 0) {
                showToast(`成功驳回 ${successCount} 篇文案${failCount > 0 ? `，${failCount} 篇失败` : ''}`,
                         failCount > 0 ? 'warning' : 'success');

                // 从列表中移除成功驳回的内容
                contentIds.forEach((contentId, index) => {
                    if (results[index].success) {
                        removeContentFromList(contentId);
                    }
                });

                // 重置选择状态
                updateSelectedCount();
            } else {
                showToast('批量驳回失败，请重试', 'error');
            }

            // 关闭模态框
            closeModal('batchRejectModal');
        })
        .catch(error => {
            console.error('批量驳回失败:', error);
            showToast('批量驳回失败，请重试', 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
}

// 从列表中移除内容行
function removeContentFromList(contentId) {
    // 查找包含该内容ID的行
    const contentRow = document.querySelector(`[data-content-id="${contentId}"]`);
    if (contentRow) {
        // 获取该行的图片数量（用于更新统计）
        const imageCountBadge = contentRow.querySelector('.badge.bg-success');
        let imageCount = 0;
        if (imageCountBadge) {
            const badgeText = imageCountBadge.textContent.trim();
            const match = badgeText.match(/(\d+)\s*张图片/);
            imageCount = match ? parseInt(match[1]) : 0;
        }

        // 添加淡出动画
        contentRow.style.transition = 'opacity 0.3s ease';
        contentRow.style.opacity = '0';

        // 动画完成后移除元素
        setTimeout(() => {
            contentRow.remove();

            // 更新统计数字
            updateStatisticsAfterRemoval(imageCount);

            // 检查是否还有内容，如果没有则显示空状态
            const remainingRows = document.querySelectorAll('[data-content-id]');
            if (remainingRows.length === 0) {
                const tableBody = document.querySelector('tbody');
                if (tableBody) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">
                                <i class="bi bi-inbox fs-1"></i>
                                <div class="mt-2">暂无待审核的文案</div>
                            </td>
                        </tr>
                    `;
                }
            }
        }, 300);
    }
}

// 更新统计数字（移除内容后）
function updateStatisticsAfterRemoval(removedImageCount) {
    // 重新获取统计数据而不是简单减少数字
    refreshStatistics();
}

// 刷新统计数据
function refreshStatistics() {
    // 获取当前的筛选参数
    const clientId = document.getElementById('client_filter')?.value || '';
    const taskId = document.getElementById('task_filter')?.value || '';

    // 构建请求URL
    let url = window.location.pathname;
    const params = new URLSearchParams();
    if (clientId) params.set('client_id', clientId);
    if (taskId) params.set('task_id', taskId);
    if (params.toString()) {
        url += '?' + params.toString();
    }

    // 发送AJAX请求获取最新统计数据
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 解析返回的HTML，提取统计数据
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 更新统计卡片
        const statisticsRow = doc.querySelector('.row.mb-4');
        if (statisticsRow) {
            const currentStatisticsRow = document.querySelector('.row.mb-4');
            if (currentStatisticsRow) {
                currentStatisticsRow.innerHTML = statisticsRow.innerHTML;
            }
        }
    })
    .catch(error => {
        console.error('刷新统计数据失败:', error);
    });
}

// 初始化客户任务联动功能
function initClientTaskLinkage() {
    console.log('=== 初始化客户任务联动功能 ===');

    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');

    console.log('元素查找结果:');
    console.log('- clientSelect:', clientSelect);
    console.log('- taskSelect:', taskSelect);

    if (!clientSelect || !taskSelect) {
        console.error('❌ 找不到客户或任务选择框');
        console.log('页面中的所有select元素:', document.querySelectorAll('select'));
        return;
    }

    console.log('✅ 找到客户和任务选择框');
    console.log('客户选择框当前值:', clientSelect.value);
    console.log('任务选择框当前值:', taskSelect.value);

    // 绑定客户选择变更事件
    clientSelect.onchange = function() {
        console.log('=== 客户选择变更事件被触发 ===');
        const clientId = this.value;
        console.log('选择的客户ID:', clientId);
        console.log('客户选择框对象:', this);

        if (clientId && clientId !== '') {
            // 显示加载状态
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;

            console.log('开始获取客户任务列表...');

            // 调用API
            fetch(`/simple/api/get-final-review-client-tasks/${clientId}`)
                .then(response => {
                    console.log('API响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('API响应数据:', data);

                    // 更新任务选择框
                    taskSelect.innerHTML = '<option value="">全部任务</option>';
                    taskSelect.disabled = false;

                    if (data.success && data.tasks) {
                        data.tasks.forEach(task => {
                            const option = document.createElement('option');
                            option.value = task.id;
                            option.textContent = task.name;
                            taskSelect.appendChild(option);
                        });
                        console.log(`✅ 成功加载 ${data.tasks.length} 个任务`);
                    } else {
                        console.log('该客户没有最终审核任务');
                    }

                    // 任务加载完成，不自动执行搜索，避免死循环
                    console.log('任务加载完成，等待用户进一步操作');
                })
                .catch(error => {
                    console.error('API调用失败:', error);
                    taskSelect.innerHTML = '<option value="">加载失败</option>';
                    taskSelect.disabled = false;
                });
        } else {
            // 选择了"全部客户"，重置任务选择框并显示所有数据
            console.log('选择了全部客户，重置任务选择框');
            taskSelect.innerHTML = '<option value="">全部任务</option>';
            taskSelect.disabled = false;

            // 重置任务选择框完成，不自动执行搜索
            console.log('任务选择框已重置');
        }
    };

    console.log('✅ 客户选择事件绑定完成');

    // 绑定任务选择变更事件
    taskSelect.onchange = function() {
        console.log('=== 任务选择变更 ===');
        console.log('选择的任务ID:', this.value);
        // 只记录变更，不自动执行搜索，避免死循环
        console.log('任务选择已变更，等待用户点击筛选按钮');
    };

    // 如果有默认选中的客户，只加载任务列表，不触发搜索
    if (clientSelect.value) {
        console.log('发现默认选中的客户，仅加载任务列表:', clientSelect.value);
        const clientId = clientSelect.value;

        // 只加载任务列表，不执行搜索
        if (clientId && clientId !== '') {
            taskSelect.innerHTML = '<option value="">加载中...</option>';
            taskSelect.disabled = true;

            fetch(`/simple/api/get-final-review-client-tasks/${clientId}`)
                .then(response => response.json())
                .then(data => {
                    taskSelect.innerHTML = '<option value="">全部任务</option>';
                    taskSelect.disabled = false;

                    if (data.success && data.tasks) {
                        data.tasks.forEach(task => {
                            const option = document.createElement('option');
                            option.value = task.id;
                            option.textContent = task.name;
                            // 如果URL中有选中的任务ID，设置为选中状态
                            const urlParams = new URLSearchParams(window.location.search);
                            if (urlParams.get('task_id') == task.id) {
                                option.selected = true;
                            }
                            taskSelect.appendChild(option);
                        });
                        console.log(`✅ 初始化时加载了 ${data.tasks.length} 个任务`);
                    }
                })
                .catch(error => {
                    console.error('初始化加载任务失败:', error);
                    taskSelect.innerHTML = '<option value="">加载失败</option>';
                    taskSelect.disabled = false;
                });
        }
    }
}

// 执行最终审核搜索的函数
function performFinalReviewSearch() {
    console.log('=== 执行最终审核搜索 ===');

    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');
    const searchInput = document.querySelector('input[name="search"]');

    const clientId = clientSelect ? clientSelect.value : '';
    const taskId = taskSelect ? taskSelect.value : '';
    const search = searchInput ? searchInput.value : '';

    console.log('搜索参数:', { clientId, taskId, search });

    // 构建查询参数
    const params = new URLSearchParams();
    if (clientId) params.append('client_id', clientId);
    if (taskId) params.append('task_id', taskId);
    if (search) params.append('search', search);

    const url = `/simple/final-review?${params.toString()}`;
    console.log('请求URL:', url);

    // 发送AJAX请求
    fetch(url, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 解析HTML，精确更新表格内容，避免重复表头
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 查找新的表格tbody内容
        const newTbody = doc.querySelector('.table-responsive table tbody');
        const currentTbody = document.querySelector('.table-responsive table tbody');

        if (newTbody && currentTbody) {
            // 只更新表格tbody内容，保持表头不变
            currentTbody.innerHTML = newTbody.innerHTML;
            console.log('✅ 表格内容更新完成');

            // 重新初始化选择状态
            updateSelectedCount();
        } else {
            // 如果找不到tbody，查找整个文案列表卡片
            const newContentCard = doc.querySelector('.card .card-body');
            const currentContentCard = document.querySelector('.card .card-body');

            if (newContentCard && currentContentCard) {
                // 查找包含表格的卡片
                const cardWithTable = document.querySelector('.card:has(.table-responsive)');
                const newCardWithTable = doc.querySelector('.card:has(.table-responsive)');

                if (cardWithTable && newCardWithTable) {
                    cardWithTable.innerHTML = newCardWithTable.innerHTML;
                    console.log('✅ 文案列表卡片更新完成');

                    // 重新初始化选择状态
                    updateSelectedCount();
                } else {
                    console.error('无法找到文案列表卡片，刷新整个页面');
                    location.reload();
                }
            } else {
                console.error('无法找到内容区域，刷新整个页面');
                location.reload();
            }
        }

        // 更新浏览器URL（不刷新页面）
        history.pushState(null, '', url);

        console.log('✅ 搜索完成');
    })
    .catch(error => {
        console.error('搜索失败:', error);
        showToast('搜索失败，请重试', 'error');
    });
}
</script>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2><i class="bi bi-check2-square"></i> 最终审核
                {% if client %}
                - {{ client.name }}
                {% endif %}
            </h2>
            <p class="text-muted">对已上传图片的文案进行最终审核</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('main_simple.final_review') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回客户列表
            </a>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-warning">{{ statistics.total_content_count if statistics else content_data|length }}</h4>
                    <small class="text-muted">待最终审核</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info">{{ statistics.total_image_count if statistics else (content_data|map(attribute='image_count')|sum) }}</h4>
                    <small class="text-muted">总图片数量</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary">{{ pagination.page }}/{{ pagination.pages }}</h4>
                    <small class="text-muted">当前页/总页数</small>
                </div>
            </div>
        </div>

    </div>

    <!-- 筛选条件 -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-title mb-0">筛选条件</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3" id="filterForm">
                <div class="col-md-3">
                    <label for="task_filter" class="form-label">任务</label>
                    <select class="form-select" id="task_filter" name="task_id">
                        <option value="">全部任务</option>
                        {% for value, label in form.task_id.choices %}
                            {% if value != 0 %}
                            <option value="{{ value }}"
                                    {% if request.args.get('task_id') == value|string %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">搜索</label>
                    {{ form.search(class="form-control", placeholder="搜索标题或内容") }}
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> 筛选
                    </button>
                    <a href="{{ url_for('main_simple.final_review') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </a>
                    <button type="button" class="btn btn-outline-success me-2" onclick="showBatchApproveModal()" id="batchApproveBtn" disabled>
                        <i class="bi bi-check-circle"></i> 批量通过
                    </button>
                    <button type="button" class="btn btn-outline-danger me-2" onclick="showBatchRejectModal()" id="batchRejectBtn" disabled>
                        <i class="bi bi-x-circle"></i> 批量驳回
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 文案列表 -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">待审核文案列表</h6>
        </div>
        <div class="card-body">
            {% if content_data %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="80">
                                    <input type="checkbox" id="selectAllHeader" onchange="toggleSelectAll(this)">
                                    <label for="selectAllHeader" class="ms-1 small">全选</label>
                                </th>
                                <th width="200">信息面板</th>
                                <th>文案内容</th>
                                <th width="250">配图预览</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in content_data %}
                            <tr data-content-id="{{ item.content.id }}">
                                <td>
                                    <input type="checkbox" class="content-checkbox" value="{{ item.content.id }}">
                                </td>
                                <!-- 左侧信息面板 -->
                                <td>
                                    <div class="info-panel">
                                        <!-- 话题标签 -->
                                        <div class="section mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-tags text-primary"></i> 话题标签
                                            </h6>
                                            <div class="tags-container">
                                                {% if item.content.topics_list %}
                                                    {% for topic in item.content.topics_list %}
                                                    <span class="badge bg-primary me-1">{{ topic if topic.startswith('#') else '#' + topic }}</span>
                                                    {% endfor %}
                                                {% else %}
                                                    <span class="text-muted small">暂无话题</span>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- 位置信息 -->
                                        <div class="section mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-geo-alt text-danger"></i> 位置信息
                                            </h6>
                                            <div class="location-info">
                                                {% if item.content.location %}
                                                    <span class="text-primary">{{ item.content.location }}</span>
                                                {% else %}
                                                    <span class="text-muted small">暂无位置信息</span>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- @用户 -->
                                        {% if item.content.at_users_list %}
                                        <div class="section mb-3">
                                            <h6 class="section-title">
                                                <i class="bi bi-at text-success"></i> @用户
                                            </h6>
                                            <div class="tags-container">
                                                {% for user in item.content.at_users_list %}
                                                <span class="badge bg-success me-1">{{ user if user.startswith('@') else '@' + user }}</span>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        {% endif %}

                                        <!-- 用户信息 -->
                                        <div class="section">
                                            <h6 class="section-title">
                                                <i class="bi bi-person text-info"></i> 用户
                                            </h6>
                                            <div class="user-info">
                                                <span class="badge bg-info">{{ item.content.client.name if item.content.client else '未知' }}</span>
                                                <div class="text-muted small mt-1">
                                                    {{ item.content.created_at.strftime('%m-%d %H:%M') if item.content.created_at else '未知' }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <!-- 文案内容 -->
                                <td>
                                    <div class="content-preview-card">
                                        <div class="content-info">
                                            <h6 class="content-title mb-2">
                                                <i class="bi bi-file-text text-primary"></i>
                                                {{ item.content.title }}
                                            </h6>
                                            <div class="content-text">
                                                <p class="mb-0">{{ item.content.content }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <!-- 配图预览 -->
                                <td>
                                    {% if item.images %}
                                    <div class="images-preview">
                                        <div class="mb-2">
                                            <span class="badge bg-success">{{ item.image_count }} 张图片</span>
                                        </div>
                                        <div class="image-grid-square">
                                            {% for image in item.images %}
                                            <div class="image-item-square" onclick="showImageModal('{{ url_for('static', filename='uploads/' + image.image_path) }}', '{{ image.original_name }}')">
                                                <img src="{{ url_for('static', filename='uploads/' + (image.thumbnail_path or image.image_path)) }}"
                                                     class="square-thumbnail"
                                                     title="{{ image.original_name }}">
                                                <div class="image-order-square">{{ loop.index }}</div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="no-images text-center">
                                        <i class="bi bi-image text-muted"></i>
                                        <div class="text-muted small">暂无配图</div>
                                    </div>
                                    {% endif %}
                                </td>
                                <!-- 操作按钮 -->
                                <td>
                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                onclick="viewContent({{ item.content.id }})">
                                            <i class="bi bi-eye"></i> 查看详情
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="approveContent({{ item.content.id }})">
                                            <i class="bi bi-check"></i> 通过审核
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="currentContentId = {{ item.content.id }}; rejectContent({{ item.content.id }}, event)">
                                            <i class="bi bi-x"></i> 驳回
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                <nav aria-label="分页导航">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            {% if client %}
                                <a class="page-link" href="{{ url_for('main_simple.final_review_client_detail', client_id=client.id, page=pagination.prev_num, task_id=request.args.get('task_id', '')) }}">上一页</a>
                            {% else %}
                                <a class="page-link" href="{{ url_for('main_simple.final_review', page=pagination.prev_num) }}">上一页</a>
                            {% endif %}
                        </li>
                        {% endif %}

                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                <li class="page-item">
                                    {% if client %}
                                        <a class="page-link" href="{{ url_for('main_simple.final_review_client_detail', client_id=client.id, page=page_num, task_id=request.args.get('task_id', '')) }}">{{ page_num }}</a>
                                    {% else %}
                                        <a class="page-link" href="{{ url_for('main_simple.final_review', page=page_num) }}">{{ page_num }}</a>
                                    {% endif %}
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                        <li class="page-item">
                            {% if client %}
                                <a class="page-link" href="{{ url_for('main_simple.final_review_client_detail', client_id=client.id, page=pagination.next_num, task_id=request.args.get('task_id', '')) }}">下一页</a>
                            {% else %}
                                <a class="page-link" href="{{ url_for('main_simple.final_review', page=pagination.next_num) }}">下一页</a>
                            {% endif %}
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <h5 class="text-muted mt-3">暂无待最终审核的文案</h5>
                    <p class="text-muted">请先完成文案的图片上传</p>
                    <button type="button" class="btn btn-outline-primary" onclick="showPage('image-upload')">
                        <i class="bi bi-arrow-left"></i> 去图片上传
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 图片查看模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1" style="z-index: 1070;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">查看图片</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center" style="background: #000;">
                <img id="imageModalImg" src="" class="img-fluid" style="max-height: 80vh; border-radius: 4px;">
            </div>
        </div>
    </div>
</div>



<!-- 自定义驳回理由模态框 -->
<div class="modal fade" id="customRejectModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle"></i> 驳回文案
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customRejectReason" class="form-label fw-bold">驳回理由</label>
                    <textarea class="form-control" id="customRejectReason" rows="4"
                              placeholder="请详细说明驳回的原因，以便作者进行针对性修改..."
                              style="resize: vertical;"></textarea>
                    <div class="form-text text-muted">
                        <i class="bi bi-info-circle"></i> 请提供具体的修改建议，帮助作者改进内容
                    </div>
                </div>

                <!-- 快捷理由选择 -->
                <div class="mb-3">
                    <label class="form-label fw-bold">常用理由（点击快速填入）</label>
                    <div class="d-flex flex-wrap gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reason-btn"
                                onclick="fillQuickReason('内容质量不符合要求，需要重新编写')">
                            内容质量不达标
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reason-btn"
                                onclick="fillQuickReason('标题不够吸引人，建议重新设计')">
                            标题需要优化
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reason-btn"
                                onclick="fillQuickReason('图片质量需要提升，建议更换高质量图片')">
                            图片质量问题
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reason-btn"
                                onclick="fillQuickReason('文案长度不合适，需要调整')">
                            长度需调整
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reason-btn"
                                onclick="fillQuickReason('话题标签需要优化，更贴合内容主题')">
                            标签需优化
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm quick-reason-btn"
                                onclick="fillQuickReason('内容与品牌调性不符，需要调整风格')">
                            风格不符
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-danger" onclick="submitCustomReject()">
                    <i class="bi bi-check-circle"></i> 确认驳回
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 改进的驳回理由模态框 - 支持驳回类型选择 -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle"></i> 驳回文案
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm" method="POST" action="/simple/api/final-review/submit-reject">
                    <!-- 隐藏字段存储文案ID -->
                    <input type="hidden" id="rejectContentId" name="content_id" value="">
                    <!-- 驳回类型选择 -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="bi bi-list-check"></i> 请选择问题类型：
                        </label>
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="card h-100 rejection-type-card" data-type="content">
                                    <input class="form-check-input d-none" type="radio" name="rejection_type"
                                           value="content" id="type_content" checked>
                                    <label class="card-body text-center p-3 w-100 h-100 d-flex flex-column justify-content-center" for="type_content">
                                        <i class="bi bi-file-text fs-2 text-primary d-block mb-2"></i>
                                        <strong class="d-block">文案问题</strong>
                                        <small class="d-block text-muted mt-1">
                                            标题、内容、话题等需要修改
                                        </small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card h-100 rejection-type-card" data-type="image">
                                    <input class="form-check-input d-none" type="radio" name="rejection_type"
                                           value="image" id="type_image">
                                    <label class="card-body text-center p-3 w-100 h-100 d-flex flex-column justify-content-center" for="type_image">
                                        <i class="bi bi-image fs-2 text-success d-block mb-2"></i>
                                        <strong class="d-block">图片问题</strong>
                                        <small class="d-block text-muted mt-1">
                                            图片质量、内容、数量等需要调整
                                        </small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card h-100 rejection-type-card" data-type="both">
                                    <input class="form-check-input d-none" type="radio" name="rejection_type"
                                           value="both" id="type_both">
                                    <label class="card-body text-center p-3 w-100 h-100 d-flex flex-column justify-content-center" for="type_both">
                                        <i class="bi bi-exclamation-circle fs-2 text-danger d-block mb-2"></i>
                                        <strong class="d-block">两者都有问题</strong>
                                        <small class="d-block text-muted mt-1">
                                            文案和图片都需要重新处理
                                        </small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 流转说明 -->
                    <div class="alert alert-info mb-4" id="flowDescription">
                        <i class="bi bi-info-circle"></i>
                        <strong>流转说明：</strong>
                        <span id="flowText">选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容</span>
                    </div>

                    <!-- 快捷理由选择 - 动态更新 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">常用驳回理由：</label>
                        <div id="quickReasons" class="d-flex flex-wrap gap-2 mb-3">
                            <!-- 快捷理由按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 详细理由输入 -->
                    <div class="mb-3">
                        <label for="rejectReason" class="form-label fw-bold">详细说明：</label>
                        <textarea class="form-control" id="rejectReason" name="reason" rows="4"
                                  placeholder="请详细说明需要修改的地方，以便创作者更好地理解和改进..." required></textarea>
                        <div class="form-text">
                            <i class="bi bi-lightbulb"></i>
                            建议提供具体的修改建议，这样可以提高修改效率
                        </div>
                    </div>

                    <!-- 表单按钮 -->
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x"></i> 取消
                        </button>
                        <button type="submit" class="btn btn-danger" id="submitRejectBtn">
                            <i class="bi bi-check"></i> 确认驳回
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.rejection-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.rejection-type-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.rejection-type-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.2);
}

.rejection-type-card label {
    cursor: pointer;
    margin: 0;
    transition: all 0.2s ease;
}

.rejection-type-card:hover label {
    color: #007bff;
}

.rejection-type-card.selected label {
    color: #007bff;
}

/* 隐藏单选框但保持功能 */
.rejection-type-card .form-check-input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

/* 添加选中状态的视觉指示器 */
.rejection-type-card.selected::before {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

/* 快捷理由标签样式 */
.quick-reason-tag {
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid #007bff;
    background-color: #fff;
    color: #007bff;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    user-select: none;
}

.quick-reason-tag:hover {
    background-color: #e3f2fd;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,123,255,0.2);
}

.quick-reason-tag.active {
    background-color: #007bff;
    color: white;
    border-color: #0056b3;
    box-shadow: 0 2px 6px rgba(0,123,255,0.3);
}

.quick-reason-tag.active:hover {
    background-color: #0056b3;
    border-color: #004085;
}
</style>

<!-- 批量通过模态框 -->
<div class="modal fade" id="batchApproveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle"></i> 批量通过审核
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-success">
                    <i class="bi bi-info-circle"></i>
                    您即将批量通过 <span id="batchApproveCount">0</span> 篇文案的最终审核。
                </div>

                <div class="text-center">
                    <p class="mb-0">确认后，所有选中的文案将通过最终审核。</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-success" onclick="submitBatchApprove()">
                    <i class="bi bi-check-circle"></i> 确认批量通过
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量驳回模态框 -->
<div class="modal fade" id="batchRejectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle"></i> 批量驳回文案
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning mb-4">
                    <i class="bi bi-info-circle"></i>
                    您即将批量驳回 <span id="batchRejectCount">0</span> 篇文案，所有选中的文案将使用相同的驳回类型和理由。
                </div>

                <!-- 驳回类型选择 -->
                <div class="mb-4">
                    <label class="form-label fw-bold">
                        <i class="bi bi-list-check"></i> 请选择问题类型：
                    </label>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="card h-100 rejection-type-card" data-type="content">
                                <input class="form-check-input d-none" type="radio" name="batch_rejection_type"
                                       value="content" id="batch_type_content" checked>
                                <label class="card-body text-center p-3 w-100 h-100 d-flex flex-column justify-content-center" for="batch_type_content">
                                    <i class="bi bi-file-text fs-2 text-primary d-block mb-2"></i>
                                    <strong class="d-block">文案问题</strong>
                                    <small class="d-block text-muted mt-1">
                                        标题、内容、话题等需要修改
                                    </small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100 rejection-type-card" data-type="image">
                                <input class="form-check-input d-none" type="radio" name="batch_rejection_type"
                                       value="image" id="batch_type_image">
                                <label class="card-body text-center p-3 w-100 h-100 d-flex flex-column justify-content-center" for="batch_type_image">
                                    <i class="bi bi-image fs-2 text-success d-block mb-2"></i>
                                    <strong class="d-block">图片问题</strong>
                                    <small class="d-block text-muted mt-1">
                                        图片质量、内容、数量等需要调整
                                    </small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100 rejection-type-card" data-type="both">
                                <input class="form-check-input d-none" type="radio" name="batch_rejection_type"
                                       value="both" id="batch_type_both">
                                <label class="card-body text-center p-3 w-100 h-100 d-flex flex-column justify-content-center" for="batch_type_both">
                                    <i class="bi bi-exclamation-circle fs-2 text-danger d-block mb-2"></i>
                                    <strong class="d-block">两者都有问题</strong>
                                    <small class="d-block text-muted mt-1">
                                        文案和图片都需要重新处理
                                    </small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 流转说明 -->
                <div class="alert alert-info mb-4" id="batchFlowDescription">
                    <i class="bi bi-info-circle"></i>
                    <strong>流转说明：</strong>
                    <span id="batchFlowText">选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容</span>
                </div>

                <!-- 快捷理由选择 - 动态更新 -->
                <div class="mb-3">
                    <label class="form-label fw-bold">常用驳回理由：</label>
                    <div id="batchQuickReasons" class="d-flex flex-wrap gap-2 mb-3">
                        <!-- 快捷理由按钮将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 详细理由输入 -->
                <div class="mb-3">
                    <label for="batchRejectReason" class="form-label fw-bold">详细说明：</label>
                    <textarea class="form-control" id="batchRejectReason" rows="4"
                              placeholder="请详细说明需要修改的地方，以便创作者更好地理解和改进..." required></textarea>
                    <div class="form-text">
                        <i class="bi bi-lightbulb"></i>
                        建议提供具体的修改建议，这样可以提高修改效率
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x"></i> 取消
                </button>
                <button type="button" class="btn btn-danger" onclick="submitBatchReject()" id="batchSubmitRejectBtn">
                    <i class="bi bi-check"></i> 确认批量驳回
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// currentContentId 已在页面顶部声明，这里不需要重复声明

// 监听复选框变化
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('content-checkbox')) {
        updateSelectedCount();
    }
});

// 注意：viewContent, approveContent, rejectContent 函数已在页面顶部定义

// 表单提交前的验证（替代Ajax提交）
document.addEventListener('DOMContentLoaded', function() {
    const rejectForm = document.getElementById('rejectForm');
    if (rejectForm) {
        console.log('✅ 找到驳回表单，添加提交事件监听器');
        rejectForm.addEventListener('submit', function(e) {
            console.log('=== 表单提交事件触发 ===');

            // 阻止默认的表单提交行为，避免页面跳转
            e.preventDefault();

            const contentId = document.getElementById('rejectContentId').value;
            const rejectionType = document.querySelector('input[name="rejection_type"]:checked')?.value;
            const reason = document.getElementById('rejectReason').value.trim();

            console.log('表单数据:', {
                contentId: contentId,
                rejectionType: rejectionType,
                reason: reason
            });

            if (!reason) {
                console.log('❌ 驳回理由为空，阻止提交');
                showToast('请输入驳回理由', 'error');
                return false;
            }

            if (!contentId) {
                console.log('❌ 文案ID为空，阻止提交');
                showToast('错误：未找到文案ID', 'error');
                return false;
            }

            console.log('✅ 表单验证通过，开始AJAX提交');

            // 显示提交状态
            const submitBtn = document.getElementById('submitRejectBtn');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>提交中...';
            }

            // 使用AJAX提交，避免页面跳转
            const formData = new FormData();
            formData.append('action', 'reject');
            formData.append('review_comment', reason);
            formData.append('rejection_type', rejectionType);

            fetch(`/simple/api/final-review/${contentId}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-check"></i> 确认驳回';
                }

                if (data.success) {
                    showToast('驳回成功！', 'success');
                    // 使用统一的关闭函数，确保正确清理遮罩层
                    closeModal('rejectModal');
                    // 清空表单
                    document.getElementById('rejectReason').value = '';
                    document.getElementById('rejectContentId').value = '';
                    // 从列表中移除该行
                    removeContentFromList(contentId);
                } else {
                    showToast('操作失败：' + (data.message || '未知错误'), 'error');
                }
            })
            .catch(error => {
                console.error('驳回失败:', error);
                // 恢复按钮状态
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="bi bi-check"></i> 确认驳回';
                }
                showToast('驳回失败，请重试', 'error');
            });

            return false;
        });
    } else {
        console.error('❌ 找不到驳回表单');
    }
});

// 注意：快捷理由按钮的事件处理已经在全局事件委托中统一处理，不需要单独的函数

// 驳回类型选择相关的JavaScript代码
document.addEventListener('DOMContentLoaded', function() {
    // 驳回类型选择逻辑
    const typeCards = document.querySelectorAll('.rejection-type-card');
    const typeRadios = document.querySelectorAll('input[name="rejection_type"]');
    const flowText = document.getElementById('flowText');
    const quickReasonsContainer = document.getElementById('quickReasons');

    // 不同类型的快捷理由
    const quickReasonsByType = {
        content: [
            '标题不够吸引人，建议重新设计',
            '内容质量不符合要求，需要重新编写',
            '文案风格与品牌调性不符',
            '话题选择不当，建议调整',
            '内容过于简单，需要丰富细节'
        ],
        image: [
            '图片质量需要提升，建议更换高质量图片',
            '图片内容与文案不匹配',
            '图片数量不足，建议增加',
            '图片尺寸或格式不符合要求',
            '图片版权问题，需要更换'
        ],
        both: [
            '文案和图片整体质量都需要提升',
            '内容与图片的配合度不够',
            '整体效果不符合预期，需要重新制作',
            '品牌调性体现不够，文案和图片都需调整'
        ]
    };

    // 流转说明文本
    const flowDescriptions = {
        content: '选择"文案问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案内容',
        image: '选择"图片问题"：文案将回到<strong>图片管理</strong>，只需重新上传图片',
        both: '选择"两者都有问题"：文案将回到<strong>文案管理</strong>，需要重新编写文案并上传图片'
    };

    // 注意：快捷理由按钮的事件处理已经通过全局事件委托处理，不需要单独设置

    // 监听单选框变化
    typeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updateRejectionTypeUI();
        });
    });

    // 卡片点击事件
    typeCards.forEach(card => {
        card.addEventListener('click', function() {
            const type = this.dataset.type;
            const radio = this.querySelector('input[type="radio"]') || document.getElementById(`type_${type}`);
            if (radio) {
                radio.checked = true;
                updateRejectionTypeUI();
            }
        });
    });

    function updateRejectionTypeUI() {
        const selectedType = document.querySelector('input[name="rejection_type"]:checked')?.value || 'content';

        // 更新卡片样式
        typeCards.forEach(card => {
            const radio = card.querySelector('input[type="radio"]');
            if (radio && radio.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
        });

        // 更新流转说明
        if (flowText) {
            flowText.innerHTML = flowDescriptions[selectedType];
        }

        // 更新快捷理由
        updateQuickReasons(selectedType);
    }

    function updateQuickReasons(type) {
        if (!quickReasonsContainer) return;

        quickReasonsContainer.innerHTML = '';

        if (quickReasonsByType[type]) {
            quickReasonsByType[type].forEach(reason => {
                const btn = document.createElement('button');
                btn.type = 'button';
                btn.className = 'btn btn-outline-primary btn-sm quick-reason-tag me-2 mb-2';
                btn.textContent = reason;
                // 不再直接绑定onclick，而是使用事件委托
                btn.setAttribute('data-reason', reason);
                quickReasonsContainer.appendChild(btn);
            });
        }
    }

    // 更新文本框内容（基于选中的标签）
    function updateReasonTextarea() {
        // 重新获取容器元素，确保引用有效
        const container = document.getElementById('quickReasons');
        if (!container) {
            console.warn('quickReasons容器不存在');
            return;
        }

        const selectedTags = container.querySelectorAll('.quick-reason-tag.active');
        const textarea = document.getElementById('rejectReason');

        if (!textarea) {
            console.warn('rejectReason文本框不存在');
            return;
        }

        // 收集选中的理由
        const selectedReasons = Array.from(selectedTags).map(tag => tag.textContent.trim());

        // 将选中的理由合并到文本框中
        if (selectedReasons.length > 0) {
            textarea.value = selectedReasons.join('\n');
        } else {
            // 如果没有选中任何标签，清空文本框
            textarea.value = '';
        }

        // 聚焦到文本框，方便用户继续编辑
        textarea.focus();

        console.log('已更新文本框内容:', textarea.value);
    }

    // 立即将函数设置为全局，确保其他地方可以调用
    window.updateRejectionTypeUI = updateRejectionTypeUI;
    window.updateQuickReasons = updateQuickReasons;
    window.updateReasonTextarea = updateReasonTextarea;

    // 初始化UI
    updateRejectionTypeUI();

    // 强制初始化快捷理由（防止初始化失败）
    setTimeout(() => {
        console.log('强制初始化快捷理由...');
        updateRejectionTypeUI();
    }, 500);
});







// 注意：showToast, showImageModal, showContentModal 函数已在页面顶部定义

console.log('最终审核页面已加载');
</script>

<!-- 额外的初始化脚本 - 确保在DOM完全加载后执行 -->
<script>
// 页面完全加载后的额外初始化
window.addEventListener('load', function() {
    console.log('=== 页面完全加载，执行额外初始化 ===');

    // 检查关键元素
    const clientSelect = document.getElementById('client_filter');
    const taskSelect = document.getElementById('task_filter');

    console.log('额外检查 - 客户选择框:', clientSelect);
    console.log('额外检查 - 任务选择框:', taskSelect);

    if (clientSelect) {
        console.log('为客户选择框重新绑定事件');
        // 移除可能存在的旧事件监听器，然后添加新的
        clientSelect.removeEventListener('change', handleClientChange);
        clientSelect.addEventListener('change', handleClientChange);

        // 也保留onchange属性作为备用
        clientSelect.onchange = handleClientChange;
    }

    if (taskSelect) {
        console.log('为任务选择框重新绑定事件');
        taskSelect.removeEventListener('change', handleTaskChange);
        taskSelect.addEventListener('change', handleTaskChange);
        taskSelect.onchange = handleTaskChange;
    }

    // 初始化选择状态
    updateSelectedCount();

    console.log('=== 额外初始化完成 ===');
});

// 确保函数在全局作用域中可用
window.handleClientChange = handleClientChange;
window.handleTaskChange = handleTaskChange;

console.log('=== 全局函数已注册 ===');
console.log('handleClientChange:', typeof window.handleClientChange);
console.log('handleTaskChange:', typeof window.handleTaskChange);
</script>
